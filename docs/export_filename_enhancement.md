# Excel导出文件名增强

## 概述

为了提高用户体验和文件管理便利性，对所有支持JSON字段平铺导出的表格增加了中文文件名和时间戳功能。

## 文件名格式

### 统一格式
```
中文表名_时间戳.xlsx
```

其中：
- **中文表名**: 使用中文描述的表格名称，便于用户识别
- **时间戳**: 13位毫秒时间戳，确保文件名唯一性
- **扩展名**: 固定为`.xlsx`

### 时间戳说明
- **格式**: 13位数字的毫秒时间戳
- **生成方式**: `System.currentTimeMillis()`
- **示例**: `1753837847067`
- **优势**: 确保每次导出的文件名都是唯一的，避免文件覆盖

## 各表文件名对照

### TB0003 - 久期资产明细表
- **旧文件名**: `durationAssetDetail_时间戳.xlsx`
- **新文件名**: `久期资产明细表_时间戳.xlsx`
- **示例**: `久期资产明细表_1753837847067.xlsx`

### TB0005 - 月度折现曲线表不含价差
- **旧文件名**: `monthlyDiscountCurve_时间戳.xlsx`
- **新文件名**: `月度折现曲线表不含价差_时间戳.xlsx`
- **示例**: `月度折现曲线表不含价差_1753837847067.xlsx`

### TB0006 - 月度折现曲线表含价差
- **旧文件名**: `monthlyDiscountCurveWithSpread_时间戳.xlsx`
- **新文件名**: `月度折现曲线表含价差_时间戳.xlsx`
- **示例**: `月度折现曲线表含价差_1753837847067.xlsx`

## 实现细节

### 代码实现
在每个控制器的导出方法中添加时间戳生成逻辑：

```java
// 生成带时间戳的中文文件名
String timestamp = String.valueOf(System.currentTimeMillis());
String fileName = "中文表名_" + timestamp;
ValueSetExcelExporter.exportExcel(list, fileName, response, "jsonFieldName");
```

### 修改的文件

#### 1. TB0003 - DurationAssetDetailController.java
```java
// 生成带时间戳的中文文件名
String timestamp = String.valueOf(System.currentTimeMillis());
String fileName = "久期资产明细表_" + timestamp;
ValueSetExcelExporter.exportExcel(list, fileName, response, "issueCashflowSet", "evalCashflowSet");
```

#### 2. TB0005 - MonthlyDiscountCurveController.java
```java
// 生成带时间戳的中文文件名
String timestamp = String.valueOf(System.currentTimeMillis());
String fileName = "月度折现曲线表不含价差_" + timestamp;
ValueSetExcelExporter.exportExcel(list, fileName, response, "monthlyDiscountRateSet");
```

#### 3. TB0006 - AdurMonthlyDiscountCurveWithSpreadController.java
```java
// 生成带时间戳的中文文件名
String timestamp = String.valueOf(System.currentTimeMillis());
String fileName = "月度折现曲线表含价差_" + timestamp;
ValueSetExcelExporter.exportExcel(list, fileName, response, "monthlyDiscountRateWithSpreadSet");
```

## 用户体验改进

### 1. 文件识别性
- **中文名称**: 用户可以直接从文件名了解文件内容
- **清晰分类**: 不同表格有明确的中文标识

### 2. 文件管理
- **时间戳**: 每次导出都有唯一的时间标识
- **避免覆盖**: 不会因为重复导出而覆盖之前的文件
- **排序友好**: 按文件名排序时，相同类型的文件会聚集在一起

### 3. 操作便利性
- **快速识别**: 用户可以快速找到需要的导出文件
- **版本管理**: 通过时间戳可以区分不同时间的导出版本

## 兼容性说明

### 浏览器支持
- **Chrome**: 完全支持中文文件名
- **Firefox**: 完全支持中文文件名
- **Safari**: 完全支持中文文件名
- **Edge**: 完全支持中文文件名

### 操作系统支持
- **Windows**: 完全支持中文文件名
- **macOS**: 完全支持中文文件名
- **Linux**: 完全支持中文文件名

### 文件系统支持
- **NTFS**: 完全支持中文文件名
- **APFS**: 完全支持中文文件名
- **ext4**: 完全支持中文文件名

## 测试验证

### 测试用例
创建了专门的测试类验证文件名格式：
- `ExportFileNameTest.java`: 验证文件名格式和时间戳唯一性

### 测试内容
1. **文件名格式验证**: 确保文件名符合预期格式
2. **时间戳格式验证**: 确保时间戳为13位数字
3. **中文字符支持**: 验证中文字符正确处理
4. **唯一性验证**: 确保连续生成的文件名不重复
5. **长度合理性**: 确保文件名长度适中

### 运行测试
```bash
cd app
mvn test -Dtest=ExportFileNameTest
```

## 注意事项

### 1. 时间戳精度
- 使用毫秒级时间戳确保唯一性
- 在高并发场景下仍能保证文件名不重复

### 2. 文件名长度
- 中文文件名 + 时间戳的总长度控制在合理范围内
- 避免超过文件系统的文件名长度限制

### 3. 字符编码
- 确保中文字符在HTTP响应头中正确编码
- ValueSetExcelExporter已处理中文文件名的编码问题

### 4. 向后兼容
- 新的文件名格式不影响现有的导出功能
- 所有JSON字段平铺功能保持不变

## 相关文件

### 修改的控制器文件
1. `app/src/main/java/com/xl/alm/app/controller/DurationAssetDetailController.java`
2. `app/src/main/java/com/xl/alm/app/controller/MonthlyDiscountCurveController.java`
3. `app/src/main/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadController.java`

### 更新的测试文件
1. `app/src/test/java/com/xl/alm/app/controller/DurationAssetDetailControllerExportTest.java`
2. `app/src/test/java/com/xl/alm/app/controller/MonthlyDiscountCurveControllerExportTest.java`
3. `app/src/test/java/com/xl/alm/app/controller/AdurMonthlyDiscountCurveWithSpreadControllerExportTest.java`

### 新增的测试文件
1. `app/src/test/java/com/xl/alm/app/util/ExportFileNameTest.java`

### 更新的文档文件
1. `docs/tb0003_cashflow_export_enhancement.md`
2. `docs/tb0005_monthly_discount_curve_export_enhancement.md`
3. `docs/tb0006_monthly_discount_curve_with_spread_export_enhancement.md`
4. `docs/export_filename_enhancement.md`

## 完成状态

✅ **已完成** - 所有支持JSON字段平铺导出的表格都已更新为中文文件名+时间戳格式，提升了用户体验和文件管理便利性。
