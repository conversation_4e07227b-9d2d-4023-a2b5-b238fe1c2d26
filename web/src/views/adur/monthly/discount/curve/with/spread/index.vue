<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="账期" prop="accountPeriod">
        <el-input
          v-model="queryParams.accountPeriod"
          placeholder="请输入账期"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="久期类型" prop="durationType">
        <el-select
          v-model="queryParams.durationType"
          placeholder="请选择久期类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_duration_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="基点类型" prop="basisPointType">
        <el-select
          v-model="queryParams.basisPointType"
          placeholder="请选择基点类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_basis_point_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="日期类型" prop="dateType">
        <el-select
          v-model="queryParams.dateType"
          placeholder="请选择日期类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_date_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="价差类型" prop="spreadType">
        <el-select
          v-model="queryParams.spreadType"
          placeholder="请选择价差类型"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_spread_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资产编号" prop="assetNumber">
        <el-input
          v-model="queryParams.assetNumber"
          placeholder="请输入资产编号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="账户名称" prop="accountName">
        <el-select
          v-model="queryParams.accountName"
          placeholder="请选择账户名称"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in dict.type.adur_account_name"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['adur:monthly:discount:curve:with:spread:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['adur:monthly:discount:curve:with:spread:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['adur:monthly:discount:curve:with:spread:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['adur:monthly:discount:curve:with:spread:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="monthlyDiscountCurveWithSpreadList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="账期" align="center" prop="accountPeriod" />
      <el-table-column label="久期类型" align="center" prop="durationType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_duration_type" :value="scope.row.durationType"/>
        </template>
      </el-table-column>
      <el-table-column label="基点类型" align="center" prop="basisPointType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_basis_point_type" :value="scope.row.basisPointType"/>
        </template>
      </el-table-column>
      <el-table-column label="日期类型" align="center" prop="dateType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_date_type" :value="scope.row.dateType"/>
        </template>
      </el-table-column>
      <el-table-column label="日期" align="center" prop="date" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.date, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="价差类型" align="center" prop="spreadType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_spread_type" :value="scope.row.spreadType"/>
        </template>
      </el-table-column>
      <el-table-column label="价差" align="center" prop="spread" />
      <el-table-column label="账户名称" align="center" prop="accountName">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.adur_account_name" :value="scope.row.accountName"/>
        </template>
      </el-table-column>
      <el-table-column label="资产名称" align="center" prop="assetName" :show-overflow-tooltip="true" />
      <el-table-column label="证券代码" align="center" prop="securityCode" />
      <el-table-column label="折现曲线标识" align="center" prop="curveId" />
      <el-table-column label="月度折现曲线利率值集" align="center" prop="monthlyDiscountRateSet" width="200">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="primary"
            @click="handleViewTermData(scope.row)"
            v-hasPermi="['adur:monthly:discount:curve:with:spread:query']"
          >查看期限数据</el-button>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['adur:monthly:discount:curve:with:spread:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['adur:monthly:discount:curve:with:spread:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改ADUR月度折现曲线含价差对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1200px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="账期" prop="accountPeriod">
              <el-input v-model="form.accountPeriod" placeholder="请输入账期" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="久期类型" prop="durationType">
              <el-select v-model="form.durationType" placeholder="请选择久期类型">
                <el-option
                  v-for="dict in dict.type.adur_duration_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="基点类型" prop="basisPointType">
              <el-select v-model="form.basisPointType" placeholder="请选择基点类型">
                <el-option
                  v-for="dict in dict.type.adur_basis_point_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="日期类型" prop="dateType">
              <el-select v-model="form.dateType" placeholder="请选择日期类型">
                <el-option
                  v-for="dict in dict.type.adur_date_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="日期" prop="date">
              <el-date-picker clearable
                v-model="form.date"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="价差类型" prop="spreadType">
              <el-select v-model="form.spreadType" placeholder="请选择价差类型">
                <el-option
                  v-for="dict in dict.type.adur_spread_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="价差" prop="spread">
              <el-input v-model="form.spread" placeholder="请输入价差" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="曲线细分类" prop="curveSubCategory">
              <el-input v-model="form.curveSubCategory" placeholder="请输入曲线细分类" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资产编号" prop="assetNumber">
              <el-input v-model="form.assetNumber" placeholder="请输入资产编号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账户名称" prop="accountName">
              <el-select v-model="form.accountName" placeholder="请选择账户名称">
                <el-option
                  v-for="dict in dict.type.adur_account_name"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="资产名称" prop="assetName">
              <el-input v-model="form.assetName" placeholder="请输入资产名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证券代码" prop="securityCode">
              <el-input v-model="form.securityCode" placeholder="请输入证券代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="折现曲线标识" prop="curveId">
              <el-input v-model="form.curveId" placeholder="请输入折现曲线标识" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="月度折现曲线利率值集" prop="monthlyDiscountRateSet">
              <el-input
                v-model="form.monthlyDiscountRateSet"
                type="textarea"
                :rows="3"
                placeholder="JSON格式数据，包含term_0到term_600的期限数据"
                readonly
              />
              <div style="margin-top: 10px;">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleEditTermData"
                  v-if="form.id"
                >编辑期限数据</el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="handleInitTermData"
                  v-if="!form.id"
                >初始化期限数据</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
import { listMonthlyDiscountCurveWithSpread, getMonthlyDiscountCurveWithSpread, delMonthlyDiscountCurveWithSpread, addMonthlyDiscountCurveWithSpread, updateMonthlyDiscountCurveWithSpread, getTermData, updateTermData } from "@/api/adur/monthlyDiscountCurveWithSpread";

export default {
  name: "MonthlyDiscountCurveWithSpread",
  dicts: ['adur_duration_type', 'adur_basis_point_type', 'adur_date_type', 'adur_spread_type', 'adur_curve_sub_category', 'adur_account_name'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // ADUR月度折现曲线含价差表格数据
      monthlyDiscountCurveWithSpreadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        accountPeriod: null,
        durationType: null,
        basisPointType: null,
        dateType: null,
        date: null,
        spreadType: null,
        spread: null,
        curveSubCategory: null,
        assetNumber: null,
        accountName: null,
        assetName: null,
        securityCode: null,
        curveId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        accountPeriod: [
          { required: true, message: "账期不能为空", trigger: "blur" }
        ],
        durationType: [
          { required: true, message: "久期类型不能为空", trigger: "change" }
        ],
        basisPointType: [
          { required: true, message: "基点类型不能为空", trigger: "change" }
        ],
        dateType: [
          { required: true, message: "日期类型不能为空", trigger: "change" }
        ],
        date: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        assetNumber: [
          { required: true, message: "资产编号不能为空", trigger: "blur" }
        ],
        accountName: [
          { required: true, message: "账户名称不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询ADUR月度折现曲线含价差列表 */
    getList() {
      this.loading = true;
      listMonthlyDiscountCurveWithSpread(this.queryParams).then(response => {
        this.monthlyDiscountCurveWithSpreadList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        accountPeriod: null,
        durationType: null,
        basisPointType: null,
        dateType: null,
        date: null,
        spreadType: null,
        spread: null,
        curveSubCategory: null,
        assetNumber: null,
        accountName: null,
        assetName: null,
        securityCode: null,
        curveId: null,
        monthlyDiscountRateSet: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加ADUR月度折现曲线含价差";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMonthlyDiscountCurveWithSpread(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改ADUR月度折现曲线含价差";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMonthlyDiscountCurveWithSpread(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMonthlyDiscountCurveWithSpread(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除ADUR月度折现曲线含价差编号为"' + ids + '"的数据项？').then(function() {
        return delMonthlyDiscountCurveWithSpread(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('adur/monthly/discount/curve/with/spread/export', {
        ...this.queryParams
      })
    },
    /** 查看期限数据 */
    handleViewTermData(row) {
      getTermData(row.id).then(response => {
        if (response.code === 200) {
          this.$modal.msgInfo("期限数据：" + JSON.stringify(response.data));
        } else {
          this.$modal.msgError(response.msg || '获取期限数据失败');
        }
      }).catch(() => {
        this.$modal.msgError('获取期限数据失败');
      });
    },
    /** 编辑期限数据 */
    handleEditTermData() {
      this.$modal.msgInfo("编辑期限数据功能待实现");
    },
    /** 初始化期限数据 */
    handleInitTermData() {
      // 初始化601个期限数据为0
      const termData = {};
      for (let i = 0; i <= 600; i++) {
        termData[`term_${i}`] = 0;
      }
      this.form.monthlyDiscountRateSet = JSON.stringify(termData);
      this.$modal.msgSuccess("期限数据已初始化");
    },
    /** 编辑期限数据 */
    handleEditTermData() {
      this.$modal.msgInfo("编辑期限数据功能待实现");
    },
    /** 初始化期限数据 */
    handleInitTermData() {
      // 初始化601个期限数据为0
      const termData = {};
      for (let i = 0; i <= 600; i++) {
        termData[`term_${i}`] = 0;
      }
      this.form.monthlyDiscountRateSet = JSON.stringify(termData);
      this.$modal.msgSuccess("期限数据已初始化");
    }
  }
};
</script>
