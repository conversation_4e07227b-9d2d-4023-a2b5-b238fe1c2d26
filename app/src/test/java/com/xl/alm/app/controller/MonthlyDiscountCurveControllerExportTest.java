package com.xl.alm.app.controller;

import com.xl.alm.app.dto.MonthlyDiscountCurveDTO;
import com.xl.alm.app.service.MonthlyDiscountCurveService;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockHttpServletResponse;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TB0005月度折现曲线表导出功能测试类
 * 测试月度折现曲线利率值集字段的Excel导出展开功能
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class MonthlyDiscountCurveControllerExportTest {

    @MockBean
    private MonthlyDiscountCurveService monthlyDiscountCurveService;

    private MonthlyDiscountCurveController controller;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        controller = new MonthlyDiscountCurveController();
        // 使用反射设置service
        try {
            java.lang.reflect.Field serviceField = MonthlyDiscountCurveController.class.getDeclaredField("monthlyDiscountCurveService");
            serviceField.setAccessible(true);
            serviceField.set(controller, monthlyDiscountCurveService);
        } catch (Exception e) {
            fail("设置service失败: " + e.getMessage());
        }
        
        response = new MockHttpServletResponse();
    }

    @Test
    @DisplayName("测试月度折现曲线利率值集导出功能")
    void testExportWithMonthlyDiscountRateSet() {
        // 准备测试数据
        List<MonthlyDiscountCurveDTO> testData = createTestDataWithRateSet();
        
        // Mock service返回
        when(monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoList(any()))
            .thenReturn(testData);

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                eq(testData), 
                eq("月度折现曲线数据"), 
                eq(response), 
                eq("monthlyDiscountRateSet")
            ));
        }
    }

    @Test
    @DisplayName("测试月度折现曲线JSON格式解析")
    void testMonthlyDiscountRateJsonParsing() {
        // 创建包含利率JSON的测试数据
        String testRateJson = createTestMonthlyDiscountRateJson();
        
        // 验证JSON格式正确
        assertNotNull(testRateJson);
        assertTrue(testRateJson.contains("\"0\""));
        assertTrue(testRateJson.contains("0.022459"));
        
        System.out.println("测试月度折现曲线利率JSON格式:");
        System.out.println(testRateJson);
    }

    @Test
    @DisplayName("测试字典转换功能")
    void testDictConversion() {
        // 创建包含字典值的测试数据
        List<MonthlyDiscountCurveDTO> testData = createTestDataWithDictValues();
        
        // 验证字典值设置正确
        MonthlyDiscountCurveDTO dto = testData.get(0);
        assertEquals("01", dto.getDateType()); // 原始字典值
        assertEquals("01", dto.getAccountName()); // 原始字典值
        
        System.out.println("测试字典转换前的数据:");
        System.out.println("日期类型: " + dto.getDateType());
        System.out.println("账户名称: " + dto.getAccountName());
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportWithEmptyData() {
        // Mock service返回空列表
        when(monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoList(any()))
            .thenReturn(new ArrayList<>());

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                any(List.class), 
                eq("月度折现曲线数据"), 
                eq(response), 
                eq("monthlyDiscountRateSet")
            ));
        }
    }

    /**
     * 创建包含利率值集的测试数据
     */
    private List<MonthlyDiscountCurveDTO> createTestDataWithRateSet() {
        List<MonthlyDiscountCurveDTO> testData = new ArrayList<>();
        
        // 创建第一条测试记录
        MonthlyDiscountCurveDTO dto1 = new MonthlyDiscountCurveDTO();
        dto1.setId(1L);
        dto1.setAccountPeriod("202407");
        dto1.setDateType("发行时点"); // 已转换的中文标签
        dto1.setDate(new Date());
        dto1.setAssetNumber("ASSET001");
        dto1.setAccountName("账户A"); // 已转换的中文标签
        dto1.setAssetName("测试资产1");
        dto1.setSecurityCode("SEC001");
        dto1.setCurveId("CURVE001");
        dto1.setMonthlyDiscountRateSet(createTestMonthlyDiscountRateJson());
        
        testData.add(dto1);
        
        // 创建第二条测试记录
        MonthlyDiscountCurveDTO dto2 = new MonthlyDiscountCurveDTO();
        dto2.setId(2L);
        dto2.setAccountPeriod("202407");
        dto2.setDateType("评估时点"); // 已转换的中文标签
        dto2.setDate(new Date());
        dto2.setAssetNumber("ASSET002");
        dto2.setAccountName("账户B"); // 已转换的中文标签
        dto2.setAssetName("测试资产2");
        dto2.setSecurityCode("SEC002");
        dto2.setCurveId("CURVE002");
        dto2.setMonthlyDiscountRateSet(createTestMonthlyDiscountRateJson2());
        
        testData.add(dto2);
        
        return testData;
    }

    /**
     * 创建包含字典值的测试数据（用于测试字典转换）
     */
    private List<MonthlyDiscountCurveDTO> createTestDataWithDictValues() {
        List<MonthlyDiscountCurveDTO> testData = new ArrayList<>();
        
        MonthlyDiscountCurveDTO dto = new MonthlyDiscountCurveDTO();
        dto.setId(1L);
        dto.setAccountPeriod("202407");
        dto.setDateType("01"); // 原始字典值
        dto.setDate(new Date());
        dto.setAssetNumber("ASSET001");
        dto.setAccountName("01"); // 原始字典值
        dto.setAssetName("测试资产");
        dto.setSecurityCode("SEC001");
        dto.setCurveId("CURVE001");
        dto.setMonthlyDiscountRateSet(createTestMonthlyDiscountRateJson());
        
        testData.add(dto);
        return testData;
    }

    /**
     * 创建测试月度折现曲线利率JSON数据
     */
    private String createTestMonthlyDiscountRateJson() {
        return "{\n" +
               "  \"0\": 0.022459,\n" +
               "  \"1\": 0.022459,\n" +
               "  \"2\": 0.022459,\n" +
               "  \"3\": 0.022459,\n" +
               "  \"6\": 0.025000,\n" +
               "  \"12\": 0.028000,\n" +
               "  \"24\": 0.032000,\n" +
               "  \"36\": 0.035000,\n" +
               "  \"48\": 0.038000,\n" +
               "  \"60\": 0.040000,\n" +
               "  \"120\": 0.045000,\n" +
               "  \"240\": 0.050000,\n" +
               "  \"360\": 0.052000,\n" +
               "  \"480\": 0.054000,\n" +
               "  \"600\": 0.055000\n" +
               "}";
    }

    /**
     * 创建第二个测试月度折现曲线利率JSON数据
     */
    private String createTestMonthlyDiscountRateJson2() {
        return "{\n" +
               "  \"0\": 0.020000,\n" +
               "  \"1\": 0.020500,\n" +
               "  \"2\": 0.021000,\n" +
               "  \"3\": 0.021500,\n" +
               "  \"6\": 0.023000,\n" +
               "  \"12\": 0.026000,\n" +
               "  \"24\": 0.030000,\n" +
               "  \"36\": 0.033000,\n" +
               "  \"48\": 0.036000,\n" +
               "  \"60\": 0.038000,\n" +
               "  \"120\": 0.043000,\n" +
               "  \"240\": 0.048000,\n" +
               "  \"360\": 0.050000,\n" +
               "  \"480\": 0.052000,\n" +
               "  \"600\": 0.053000\n" +
               "}";
    }
}
