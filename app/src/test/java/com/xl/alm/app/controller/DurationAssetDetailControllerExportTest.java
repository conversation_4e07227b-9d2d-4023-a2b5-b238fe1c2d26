package com.xl.alm.app.controller;

import com.xl.alm.app.dto.DurationAssetDetailDTO;
import com.xl.alm.app.service.DurationAssetDetailService;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mock.web.MockHttpServletResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TB0003久期资产明细表导出功能测试类
 * 测试评估时点现金流值集字段的Excel导出展开功能
 *
 * <AUTHOR> Assistant
 */
@SpringBootTest
public class DurationAssetDetailControllerExportTest {

    @MockBean
    private DurationAssetDetailService durationAssetDetailService;

    private DurationAssetDetailController controller;
    private MockHttpServletResponse response;

    @BeforeEach
    void setUp() {
        controller = new DurationAssetDetailController();
        // 使用反射设置service
        try {
            java.lang.reflect.Field serviceField = DurationAssetDetailController.class.getDeclaredField("durationAssetDetailService");
            serviceField.setAccessible(true);
            serviceField.set(controller, durationAssetDetailService);
        } catch (Exception e) {
            fail("设置service失败: " + e.getMessage());
        }
        
        response = new MockHttpServletResponse();
    }

    @Test
    @DisplayName("测试现金流值集导出功能")
    void testExportWithCashflowSets() {
        // 准备测试数据
        List<DurationAssetDetailDTO> testData = createTestDataWithCashflowSet();

        // Mock service返回
        when(durationAssetDetailService.selectDurationAssetDetailDtoList(any()))
            .thenReturn(testData);

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);

            // 验证ValueSetExcelExporter.exportExcel被调用，同时处理两个现金流字段
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                eq(testData),
                eq("久期资产明细数据"),
                eq(response),
                eq("issueCashflowSet"),
                eq("evalCashflowSet")
            ));
        }
    }

    @Test
    @DisplayName("测试现金流JSON格式解析")
    void testCashflowJsonParsing() {
        // 创建包含现金流JSON的测试数据
        String testIssueCashflowJson = createTestIssueCashflowJson();
        String testEvalCashflowJson = createTestEvalCashflowJson();

        // 验证发行时点现金流JSON格式正确
        assertNotNull(testIssueCashflowJson);
        assertTrue(testIssueCashflowJson.contains("\"0\""));
        assertTrue(testIssueCashflowJson.contains("\"日期\""));
        assertTrue(testIssueCashflowJson.contains("\"值\""));

        // 验证评估时点现金流JSON格式正确
        assertNotNull(testEvalCashflowJson);
        assertTrue(testEvalCashflowJson.contains("\"0\""));
        assertTrue(testEvalCashflowJson.contains("\"日期\""));
        assertTrue(testEvalCashflowJson.contains("\"值\""));

        System.out.println("测试发行时点现金流JSON格式:");
        System.out.println(testIssueCashflowJson);
        System.out.println("测试评估时点现金流JSON格式:");
        System.out.println(testEvalCashflowJson);
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportWithEmptyData() {
        // Mock service返回空列表
        when(durationAssetDetailService.selectDurationAssetDetailDtoList(any()))
            .thenReturn(new ArrayList<>());

        // 使用MockedStatic来模拟ValueSetExcelExporter的静态方法调用
        try (MockedStatic<ValueSetExcelExporter> mockedExporter = Mockito.mockStatic(ValueSetExcelExporter.class)) {
            // 执行导出
            controller.export(response, null);
            
            // 验证ValueSetExcelExporter.exportExcel被调用，同时处理两个现金流字段
            mockedExporter.verify(() -> ValueSetExcelExporter.exportExcel(
                any(List.class),
                eq("久期资产明细数据"),
                eq(response),
                eq("issueCashflowSet"),
                eq("evalCashflowSet")
            ));
        }
    }

    /**
     * 创建包含现金流值集的测试数据
     */
    private List<DurationAssetDetailDTO> createTestDataWithCashflowSet() {
        List<DurationAssetDetailDTO> testData = new ArrayList<>();
        
        // 创建第一条测试记录
        DurationAssetDetailDTO dto1 = new DurationAssetDetailDTO();
        dto1.setId(1L);
        dto1.setAccountPeriod("202407");
        dto1.setAssetNumber("BOND001");
        dto1.setAccountName("01"); // 字典值，导出时会转换为中文
        dto1.setAssetName("测试债券1");
        dto1.setSecurityCode("SEC001");
        dto1.setAssetSubCategory("国债");
        dto1.setHoldingFaceValue(new BigDecimal("1000000"));
        dto1.setMarketValue(new BigDecimal("1050000"));
        dto1.setBookBalance(new BigDecimal("1020000"));
        dto1.setBookValue(new BigDecimal("1020000"));
        dto1.setCouponRate(new BigDecimal("0.035"));
        dto1.setPaymentMethod("01"); // 字典值，导出时会转换为中文
        dto1.setCurveId("CURVE001");
        dto1.setIssueCashflowSet(createTestIssueCashflowJson());
        dto1.setEvalCashflowSet(createTestEvalCashflowJson());
        
        testData.add(dto1);
        
        // 创建第二条测试记录
        DurationAssetDetailDTO dto2 = new DurationAssetDetailDTO();
        dto2.setId(2L);
        dto2.setAccountPeriod("202407");
        dto2.setAssetNumber("BOND002");
        dto2.setAccountName("02"); // 字典值
        dto2.setAssetName("测试债券2");
        dto2.setSecurityCode("SEC002");
        dto2.setAssetSubCategory("企业债");
        dto2.setHoldingFaceValue(new BigDecimal("2000000"));
        dto2.setMarketValue(new BigDecimal("2100000"));
        dto2.setBookBalance(new BigDecimal("2050000"));
        dto2.setBookValue(new BigDecimal("2050000"));
        dto2.setCouponRate(new BigDecimal("0.045"));
        dto2.setPaymentMethod("02"); // 字典值
        dto2.setCurveId("CURVE002");
        dto2.setIssueCashflowSet(createTestIssueCashflowJson2());
        dto2.setEvalCashflowSet(createTestEvalCashflowJson2());
        
        testData.add(dto2);
        
        return testData;
    }

    /**
     * 创建测试发行时点现金流JSON数据
     */
    private String createTestIssueCashflowJson() {
        return "{\n" +
               "  \"0\": {\"日期\": \"2024-01-31\", \"值\": \"0\"},\n" +
               "  \"1\": {\"日期\": \"2024-02-29\", \"值\": \"0\"},\n" +
               "  \"2\": {\"日期\": \"2024-03-31\", \"值\": \"0\"},\n" +
               "  \"6\": {\"日期\": \"2024-07-31\", \"值\": \"17500\"},\n" +
               "  \"12\": {\"日期\": \"2025-01-31\", \"值\": \"35000\"},\n" +
               "  \"18\": {\"日期\": \"2025-07-31\", \"值\": \"17500\"},\n" +
               "  \"24\": {\"日期\": \"2026-01-31\", \"值\": \"1035000\"},\n" +
               "  \"600\": {\"日期\": \"2074-01-31\", \"值\": \"0\"}\n" +
               "}";
    }

    /**
     * 创建第二个测试发行时点现金流JSON数据
     */
    private String createTestIssueCashflowJson2() {
        return "{\n" +
               "  \"0\": {\"日期\": \"2024-01-31\", \"值\": \"0\"},\n" +
               "  \"1\": {\"日期\": \"2024-02-29\", \"值\": \"0\"},\n" +
               "  \"3\": {\"日期\": \"2024-04-30\", \"值\": \"22500\"},\n" +
               "  \"6\": {\"日期\": \"2024-07-31\", \"值\": \"22500\"},\n" +
               "  \"9\": {\"日期\": \"2024-10-31\", \"值\": \"22500\"},\n" +
               "  \"12\": {\"日期\": \"2025-01-31\", \"值\": \"22500\"},\n" +
               "  \"15\": {\"日期\": \"2025-04-30\", \"值\": \"22500\"},\n" +
               "  \"18\": {\"日期\": \"2025-07-31\", \"值\": \"22500\"},\n" +
               "  \"21\": {\"日期\": \"2025-10-31\", \"值\": \"22500\"},\n" +
               "  \"24\": {\"日期\": \"2026-01-31\", \"值\": \"2022500\"},\n" +
               "  \"600\": {\"日期\": \"2074-01-31\", \"值\": \"0\"}\n" +
               "}";
    }

    /**
     * 创建测试评估时点现金流JSON数据
     */
    private String createTestEvalCashflowJson() {
        return "{\n" +
               "  \"0\": {\"日期\": \"2024-07-31\", \"值\": \"0\"},\n" +
               "  \"1\": {\"日期\": \"2024-08-31\", \"值\": \"0\"},\n" +
               "  \"2\": {\"日期\": \"2024-09-30\", \"值\": \"0\"},\n" +
               "  \"6\": {\"日期\": \"2025-01-31\", \"值\": \"17500\"},\n" +
               "  \"12\": {\"日期\": \"2025-07-31\", \"值\": \"35000\"},\n" +
               "  \"18\": {\"日期\": \"2026-01-31\", \"值\": \"17500\"},\n" +
               "  \"24\": {\"日期\": \"2026-07-31\", \"值\": \"1035000\"},\n" +
               "  \"600\": {\"日期\": \"2074-07-31\", \"值\": \"0\"}\n" +
               "}";
    }

    /**
     * 创建第二个测试评估时点现金流JSON数据
     */
    private String createTestEvalCashflowJson2() {
        return "{\n" +
               "  \"0\": {\"日期\": \"2024-07-31\", \"值\": \"0\"},\n" +
               "  \"1\": {\"日期\": \"2024-08-31\", \"值\": \"0\"},\n" +
               "  \"3\": {\"日期\": \"2024-10-31\", \"值\": \"22500\"},\n" +
               "  \"6\": {\"日期\": \"2025-01-31\", \"值\": \"22500\"},\n" +
               "  \"9\": {\"日期\": \"2025-04-30\", \"值\": \"22500\"},\n" +
               "  \"12\": {\"日期\": \"2025-07-31\", \"值\": \"22500\"},\n" +
               "  \"15\": {\"日期\": \"2025-10-31\", \"值\": \"22500\"},\n" +
               "  \"18\": {\"日期\": \"2026-01-31\", \"值\": \"22500\"},\n" +
               "  \"21\": {\"日期\": \"2026-04-30\", \"值\": \"22500\"},\n" +
               "  \"24\": {\"日期\": \"2026-07-31\", \"值\": \"2022500\"},\n" +
               "  \"600\": {\"日期\": \"2074-07-31\", \"值\": \"0\"}\n" +
               "}";
    }
}
