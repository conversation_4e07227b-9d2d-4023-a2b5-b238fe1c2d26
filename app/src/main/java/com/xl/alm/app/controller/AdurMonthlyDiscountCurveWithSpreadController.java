package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.AdurMonthlyDiscountCurveWithSpreadDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.AdurMonthlyDiscountCurveWithSpreadQuery;
import com.xl.alm.app.service.AdurMonthlyDiscountCurveWithSpreadService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import com.xl.alm.app.util.ValueSetExcelExporter;
import com.jd.lightning.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * ADUR月度折现曲线表含价差 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/monthly/discount/curve/with/spread")
public class AdurMonthlyDiscountCurveWithSpreadController extends BaseController {

    @Autowired
    private AdurMonthlyDiscountCurveWithSpreadService adurMonthlyDiscountCurveWithSpreadService;

    /**
     * 查询ADUR月度折现曲线含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:list')")
    @GetMapping("/list")
    public TableDataInfo list(AdurMonthlyDiscountCurveWithSpreadQuery adurMonthlyDiscountCurveWithSpreadQuery) {
        startPage();
        List<AdurMonthlyDiscountCurveWithSpreadDTO> list = adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoList(adurMonthlyDiscountCurveWithSpreadQuery);
        return getDataTable(list);
    }

    /**
     * 导出ADUR月度折现曲线含价差列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:export')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AdurMonthlyDiscountCurveWithSpreadQuery adurMonthlyDiscountCurveWithSpreadQuery) {
        List<AdurMonthlyDiscountCurveWithSpreadDTO> list = adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoList(adurMonthlyDiscountCurveWithSpreadQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        // 使用ValueSetExcelExporter导出，处理monthlyDiscountRateSet字段
        // 将月度折现曲线利率含价差值集JSON字段展开为多列，key作为表头，value作为值
        ValueSetExcelExporter.exportExcel(list, "ADUR月度折现曲线含价差数据", response, "monthlyDiscountRateSet");
    }

    /**
     * 获取ADUR月度折现曲线含价差详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoById(id));
    }

    /**
     * 新增ADUR月度折现曲线含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:add')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@Valid @RequestBody AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDTO) {
        adurMonthlyDiscountCurveWithSpreadDTO.setCreateBy(getUsername());
        return toAjax(adurMonthlyDiscountCurveWithSpreadService.insertAdurMonthlyDiscountCurveWithSpreadDto(adurMonthlyDiscountCurveWithSpreadDTO));
    }

    /**
     * 修改ADUR月度折现曲线含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:edit')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@Valid @RequestBody AdurMonthlyDiscountCurveWithSpreadDTO adurMonthlyDiscountCurveWithSpreadDTO) {
        adurMonthlyDiscountCurveWithSpreadDTO.setUpdateBy(getUsername());
        return toAjax(adurMonthlyDiscountCurveWithSpreadService.updateAdurMonthlyDiscountCurveWithSpreadDto(adurMonthlyDiscountCurveWithSpreadDTO));
    }

    /**
     * 删除ADUR月度折现曲线含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:remove')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(adurMonthlyDiscountCurveWithSpreadService.deleteAdurMonthlyDiscountCurveWithSpreadDtoByIds(ids));
    }

    /**
     * 删除指定账期的ADUR月度折现曲线含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:remove')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.DELETE)
    @DeleteMapping("/period/{accountPeriod}")
    public Result removeByPeriod(@PathVariable String accountPeriod) {
        return toAjax(adurMonthlyDiscountCurveWithSpreadService.deleteAdurMonthlyDiscountCurveWithSpreadDtoByAccountPeriod(accountPeriod));
    }

    /**
     * 导入ADUR月度折现曲线含价差
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:import')")
    @Log(title = "ADUR月度折现曲线含价差", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<AdurMonthlyDiscountCurveWithSpreadDTO> util = new ExcelUtil<>(AdurMonthlyDiscountCurveWithSpreadDTO.class);
        List<AdurMonthlyDiscountCurveWithSpreadDTO> adurMonthlyDiscountCurveWithSpreadList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = adurMonthlyDiscountCurveWithSpreadService.importAdurMonthlyDiscountCurveWithSpreadDto(adurMonthlyDiscountCurveWithSpreadList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取ADUR月度折现曲线含价差导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<AdurMonthlyDiscountCurveWithSpreadDTO> util = new ExcelUtil<>(AdurMonthlyDiscountCurveWithSpreadDTO.class);
        util.exportTemplateExcel(response, "ADUR月度折现曲线含价差数据");
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        AdurMonthlyDiscountCurveWithSpreadDTO dto = adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountRateSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:with:spread:edit')")
    @Log(title = "ADUR月度折现曲线含价差期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        AdurMonthlyDiscountCurveWithSpreadDTO dto = adurMonthlyDiscountCurveWithSpreadService.selectAdurMonthlyDiscountCurveWithSpreadDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountRateSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(adurMonthlyDiscountCurveWithSpreadService.updateAdurMonthlyDiscountCurveWithSpreadDto(dto));
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list ADUR月度折现曲线含价差数据列表
     */
    private void convertDictValueToLabel(List<AdurMonthlyDiscountCurveWithSpreadDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 为每条记录转换字典值为中文标签
        for (AdurMonthlyDiscountCurveWithSpreadDTO dto : list) {
            // 转换久期类型：字典值转为中文标签
            if (dto.getDurationType() != null) {
                dto.setDurationType(DictConvertUtil.convertValueToLabel(dto.getDurationType(), "adur_duration_type"));
            }

            // 转换基点类型：字典值转为中文标签
            if (dto.getBasisPointType() != null) {
                dto.setBasisPointType(DictConvertUtil.convertValueToLabel(dto.getBasisPointType(), "adur_basis_point_type"));
            }

            // 转换日期类型：字典值转为中文标签
            if (dto.getDateType() != null) {
                dto.setDateType(DictConvertUtil.convertValueToLabel(dto.getDateType(), "adur_date_type"));
            }

            // 转换价差类型：字典值转为中文标签
            if (dto.getSpreadType() != null) {
                dto.setSpreadType(DictConvertUtil.convertValueToLabel(dto.getSpreadType(), "adur_spread_type"));
            }

            // 转换账户名称：字典值转为中文标签
            if (dto.getAccountName() != null) {
                dto.setAccountName(DictConvertUtil.convertValueToLabel(dto.getAccountName(), "adur_account_name"));
            }
        }
    }
}
