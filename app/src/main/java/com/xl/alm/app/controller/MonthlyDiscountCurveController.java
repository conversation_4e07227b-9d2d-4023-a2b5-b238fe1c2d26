package com.xl.alm.app.controller;

import com.jd.lightning.common.annotation.Log;
import com.jd.lightning.common.core.controller.BaseController;
import com.jd.lightning.common.core.domain.Result;
import com.jd.lightning.common.core.page.TableDataInfo;
import com.jd.lightning.common.enums.BusinessType;
import com.xl.alm.app.dto.MonthlyDiscountCurveDTO;
import com.xl.alm.app.dto.TermDataDTO;
import com.xl.alm.app.query.MonthlyDiscountCurveQuery;
import com.xl.alm.app.service.MonthlyDiscountCurveService;
import com.xl.alm.app.util.DictConvertUtil;
import com.xl.alm.app.util.ExcelUtil;
import com.xl.alm.app.util.TermDataUtil;
import com.xl.alm.app.util.ValueSetExcelExporter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 月度折现曲线表不含价差 Controller
 *
 * <AUTHOR> Assistant
 */
@RestController
@RequestMapping("/adur/monthly/discount/curve")
public class MonthlyDiscountCurveController extends BaseController {

    @Autowired
    private MonthlyDiscountCurveService monthlyDiscountCurveService;

    /**
     * 查询月度折现曲线列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:list')")
    @GetMapping("/list")
    public TableDataInfo list(MonthlyDiscountCurveQuery monthlyDiscountCurveQuery) {
        startPage();
        List<MonthlyDiscountCurveDTO> list = monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoList(monthlyDiscountCurveQuery);
        return getDataTable(list);
    }

    /**
     * 获取月度折现曲线详细信息
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:query')")
    @GetMapping(value = "/{id}")
    public Result getInfo(@PathVariable("id") Long id) {
        return Result.success(monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoById(id));
    }

    /**
     * 新增月度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:add')")
    @Log(title = "月度折现曲线", businessType = BusinessType.INSERT)
    @PostMapping
    public Result add(@RequestBody MonthlyDiscountCurveDTO monthlyDiscountCurveDto) {
        return toAjax(monthlyDiscountCurveService.insertMonthlyDiscountCurveDto(monthlyDiscountCurveDto));
    }

    /**
     * 修改月度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:edit')")
    @Log(title = "月度折现曲线", businessType = BusinessType.UPDATE)
    @PutMapping
    public Result edit(@RequestBody MonthlyDiscountCurveDTO monthlyDiscountCurveDto) {
        return toAjax(monthlyDiscountCurveService.updateMonthlyDiscountCurveDto(monthlyDiscountCurveDto));
    }

    /**
     * 删除月度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:remove')")
    @Log(title = "月度折现曲线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public Result remove(@PathVariable Long[] ids) {
        return toAjax(monthlyDiscountCurveService.deleteMonthlyDiscountCurveDtoByIds(ids));
    }

    /**
     * 导出月度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:export')")
    @Log(title = "月度折现曲线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MonthlyDiscountCurveQuery monthlyDiscountCurveQuery) {
        List<MonthlyDiscountCurveDTO> list = monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoList(monthlyDiscountCurveQuery);

        // 转换字典值为中文标签用于导出
        convertDictValueToLabel(list);

        // 使用ValueSetExcelExporter导出，处理monthlyDiscountRateSet字段
        // 将月度折现曲线利率值集JSON字段展开为多列，key作为表头，value作为值
        ValueSetExcelExporter.exportExcel(list, "月度折现曲线数据", response, "monthlyDiscountRateSet");
    }

    /**
     * 获取月度折现曲线导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<MonthlyDiscountCurveDTO> util = new ExcelUtil<>(MonthlyDiscountCurveDTO.class);
        util.exportTemplateExcel(response, "月度折现曲线数据");
    }

    /**
     * 导入月度折现曲线
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:import')")
    @Log(title = "月度折现曲线", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public Result importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<MonthlyDiscountCurveDTO> util = new ExcelUtil<>(MonthlyDiscountCurveDTO.class);
        List<MonthlyDiscountCurveDTO> monthlyDiscountCurveList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = monthlyDiscountCurveService.importMonthlyDiscountCurveDto(monthlyDiscountCurveList, updateSupport, operName);
        return Result.success(message);
    }

    /**
     * 获取期限数据列表
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:query')")
    @GetMapping("/termData/{id}")
    public Result getTermData(@PathVariable("id") Long id) {
        MonthlyDiscountCurveDTO dto = monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        List<TermDataDTO> termDataList = TermDataUtil.parseJsonToTermDataList(dto.getMonthlyDiscountRateSet());
        return Result.success(termDataList);
    }

    /**
     * 更新期限数据
     */
    @PreAuthorize("@ss.hasPermi('adur:monthly:discount:curve:edit')")
    @Log(title = "月度折现曲线期限数据", businessType = BusinessType.UPDATE)
    @PutMapping("/termData/{id}")
    public Result updateTermData(@PathVariable("id") Long id, @RequestBody List<TermDataDTO> termDataList) {
        // 验证期限数据完整性
        if (!TermDataUtil.validateTermDataCompleteness(termDataList)) {
            return Result.error("期限数据不完整，必须包含term_0到term_600共601个期限");
        }

        // 获取原数据
        MonthlyDiscountCurveDTO dto = monthlyDiscountCurveService.selectMonthlyDiscountCurveDtoById(id);
        if (dto == null) {
            return Result.error("数据不存在");
        }

        // 转换期限数据为JSON
        String jsonStr = TermDataUtil.convertTermDataListToJson(termDataList);
        dto.setMonthlyDiscountRateSet(jsonStr);
        dto.setUpdateBy(getUsername());

        return toAjax(monthlyDiscountCurveService.updateMonthlyDiscountCurveDto(dto));
    }

    /**
     * 将字典值转换为中文标签用于导出
     *
     * @param list 月度折现曲线数据列表
     */
    private void convertDictValueToLabel(List<MonthlyDiscountCurveDTO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 为每条记录转换字典值为中文标签
        for (MonthlyDiscountCurveDTO dto : list) {
            // 转换日期类型：字典值转为中文标签
            if (dto.getDateType() != null) {
                dto.setDateType(DictConvertUtil.convertValueToLabel(dto.getDateType(), "adur_date_type"));
            }

            // 转换账户名称：字典值转为中文标签
            if (dto.getAccountName() != null) {
                dto.setAccountName(DictConvertUtil.convertValueToLabel(dto.getAccountName(), "adur_account_name"));
            }
        }
    }
}
